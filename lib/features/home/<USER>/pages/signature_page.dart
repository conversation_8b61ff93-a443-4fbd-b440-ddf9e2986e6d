import 'dart:io';
import 'dart:typed_data';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:signature/signature.dart' as signature_package;
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/blocs/signature_page/signature_page_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/signature_page/signature_page_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';

@RoutePage()
class SignaturePage extends StatefulWidget {
  final num? questionId;
  final num? taskId;
  final num? formId;
  final String? title;

  const SignaturePage({
    super.key,
    this.questionId,
    this.taskId,
    this.formId,
    this.title,
  });

  @override
  State<SignaturePage> createState() => _SignaturePageState();
}

class _SignaturePageState extends State<SignaturePage> {
  late final signature_package.SignatureController _signatureController;
  final TextEditingController _printNameController = TextEditingController();
  late final SignaturePageCubit _cubit;
  bool _isNotAvailableSelected = false;
  bool _canSubmit = false;

  @override
  void initState() {
    super.initState();
    _signatureController = signature_package.SignatureController(
      penStrokeWidth: 2,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );

    // The Cubit is created here because its lifecycle is tied directly
    // to this page and it requires widget parameters for initialization.
    _cubit = SignaturePageCubit();

    // Add listener for real-time validation
    _printNameController.addListener(_validateSubmitButton);
    _signatureController.addListener(_validateSubmitButton);

    // Load form data if taskId and formId are provided
    if (widget.taskId != null && widget.formId != null) {
      _cubit.loadFormData(
        taskId: widget.taskId!.toInt(),
        formId: widget.formId!.toInt(),
      );
    }
  }

  @override
  void dispose() {
    _signatureController.removeListener(_validateSubmitButton);
    _printNameController.removeListener(_validateSubmitButton);
    _signatureController.dispose();
    _printNameController.dispose();
    _cubit.close();
    super.dispose();
  }

  /// Validate if submit button should be enabled
  void _validateSubmitButton() {
    final hasName = _printNameController.text.trim().isNotEmpty;
    final hasSignature =
        _signatureController.isNotEmpty || _isNotAvailableSelected;

    setState(() {
      _canSubmit = hasName && hasSignature;
    });
  }

  /// Pre-populate fields from existing signature data
  void _populateExistingData(SignaturePageLoaded state) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.existingSignedBy != null &&
          state.existingSignedBy!.isNotEmpty) {
        _printNameController.text = state.existingSignedBy!;
      }

      if (state.isNotAvailable == true) {
        setState(() {
          _isNotAvailableSelected = true;
        });
      }

      _validateSubmitButton();
    });
  }

  Future<void> _clearSignature() async {
    // Clear UI state first
    _signatureController.clear();
    _printNameController.clear(); // Also clear the name field like Android does
    setState(() {
      _isNotAvailableSelected = false;
    });
    _validateSubmitButton();

    // Clear existing signature from database if taskId and formId are available
    if (widget.taskId != null && widget.formId != null) {
      await _cubit.clearExistingSignature(
        taskId: widget.taskId!.toInt(),
        formId: widget.formId!.toInt(),
      );
    }
  }

  Future<void> _handleNotAvailable() async {
    // Toggle not available state and clear signature
    setState(() {
      _isNotAvailableSelected = !_isNotAvailableSelected;
      if (_isNotAvailableSelected) {
        _signatureController
            .clear(); // Clear signature pad when selecting "not available"
      }
    });
    _validateSubmitButton();
  }

  Future<void> _saveNotAvailableSignature() async {
    try {
      // Validate that name is provided even for "not available" signatures
      if (_printNameController.text.trim().isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'Please enter your name before submitting',
        );
        return;
      }

      if (widget.taskId != null && widget.formId != null) {
        await _cubit.saveNotAvailableSignature(
          taskId: widget.taskId!.toInt(),
          formId: widget.formId!.toInt(),
          signedBy: _printNameController.text.trim(),
        );
      } else {
        // If no taskId/formId, just navigate back
        if (context.mounted) {
          context.router.maybePop();
        }
      }
    } catch (e) {
      logger('Error saving not available signature: $e');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to save signature. Please try again.',
        );
      }
    }
  }

  Future<void> _handleSubmit() async {
    try {
      if (_printNameController.text.trim().isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'Please enter your name before submitting',
        );
        return;
      }

      // Handle "not available" signature
      if (_isNotAvailableSelected) {
        await _saveNotAvailableSignature();
        return;
      }

      // Handle regular signature
      if (_signatureController.isEmpty) {
        SnackBarService.error(
          context: context,
          message: 'Please provide a signature before submitting',
        );
        return;
      }

      final Uint8List? signatureBytes = await _signatureController.toPngBytes();
      if (signatureBytes == null) {
        if (mounted) {
          SnackBarService.error(
            context: context,
            message: 'Failed to export signature',
          );
        }
        return;
      }

      if (widget.taskId != null && widget.formId != null) {
        await _cubit.saveSignature(
          taskId: widget.taskId!.toInt(),
          formId: widget.formId!.toInt(),
          signatureBytes: signatureBytes,
          signedBy: _printNameController.text.trim(),
        );
      }
    } catch (e) {
      logger('Error submitting signature: $e');
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to submit signature. Please try again.',
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return BlocProvider.value(
      value: _cubit,
      child: BlocConsumer<SignaturePageCubit, SignaturePageState>(
        listener: (context, state) {
          if (state is SignaturePageError) {
            SnackBarService.error(context: context, message: state.message);
          } else if (state is SignaturePageLoaded) {
            // Populate existing data when state loads
            _populateExistingData(state);

            // Show success message if signature was cleared (no existing data)
            // This indicates a successful clear operation from clearExistingSignature
            if (state.existingSignatureUrl == null &&
                state.existingSignedBy == null &&
                state.isNotAvailable == null) {
              // Only show success message if we previously had existing data
              // Check if this is a clear operation by seeing if controllers are empty
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted &&
                    _signatureController.isEmpty &&
                    _printNameController.text.isEmpty &&
                    !_isNotAvailableSelected) {
                  SnackBarService.success(
                    context: context,
                    message: 'Signature cleared successfully!',
                  );
                }
              });
            }
          } else if (state is SignaturePageSaved) {
            final message = state.signatureUrl != null
                ? 'Signature saved successfully!'
                : 'Not available signature saved successfully!';
            SnackBarService.success(
              context: context,
              message: message,
            );
            context.router.maybePop();
          }
        },
        builder: (context, state) {
          return Scaffold(
            backgroundColor: AppColors.lightGrey2,
            appBar: CustomAppBar(
              title: widget.title ?? 'Signature',
            ),
            body: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildQuestionResponseTable(textTheme, state),
                        const Gap(20),
                        _buildSignatureSection(textTheme, state),
                        const Gap(20),
                        _buildPrintNameField(textTheme),
                        const Gap(100), // Space for floating action button
                      ],
                    ),
                  ),
                ),
              ],
            ),
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            floatingActionButton: _buildSubmitButton(state),
          );
        },
      ),
    );
  }

  /// A reusable container for card-like UI elements.
  Widget _buildCardContainer({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  Widget _buildQuestionResponseTable(
      TextTheme textTheme, SignaturePageState state) {
    return _buildCardContainer(
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: const BoxDecoration(
              color: AppColors.lightGrey1,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Question',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    'Response',
                    style: textTheme.montserratTitleExtraSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Container(
            padding: const EdgeInsets.all(16),
            width: double.infinity,
            child: switch (state) {
              SignaturePageLoading() => const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: CircularProgressIndicator(),
                  ),
                ),
              SignaturePageLoaded() => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _buildQuestionAnswerRows(
                      state.questionAnswers, state.form),
                ),
              _ => const Text(
                  'Loading questions and answers...',
                  style: TextStyle(fontSize: 14, color: AppColors.blackTint1),
                ),
            },
          ),
        ],
      ),
    );
  }

  List<Widget> _buildQuestionAnswerRows(
    List<entities.QuestionAnswer> questionAnswers,
    entities.Form form,
  ) {
    if (questionAnswers.isEmpty) {
      return [
        const Text(
          'No questions and answers available for this form.',
          style: TextStyle(fontSize: 14, color: AppColors.blackTint1),
        )
      ];
    }

    final List<Widget> rows = [];
    for (int i = 0; i < questionAnswers.length; i++) {
      final qa = questionAnswers[i];
      final displayData = _getQuestionAnswerDisplayData(qa, form);
      rows.add(
        _buildQuestionRow(
          displayData['question']!,
          displayData['answer']!,
        ),
      );
      if (i < questionAnswers.length - 1) {
        rows.add(const Gap(12));
      }
    }
    return rows;
  }

  /// Gets display data for a question-answer pair by looking up related entities.
  Map<String, String> _getQuestionAnswerDisplayData(
    entities.QuestionAnswer qa,
    entities.Form form,
  ) {
    // Find the question associated with this answer.
    final question = form.questions?.firstWhere(
      (q) => q.questionId == qa.questionId,
      orElse: () => entities.Question(),
    );

    // Find the measurement associated with this answer.
    final measurement = question?.measurements?.firstWhere(
      (m) => m.measurementId == qa.measurementId,
      orElse: () => entities.Measurement(),
    );

    final questionText =
        measurement?.measurementDescription ?? 'Unknown Question';

    // Determine the answer text based on its type.
    String answerText = 'Not answered';
    if (qa.measurementTextResult?.isNotEmpty ?? false) {
      answerText = qa.measurementTextResult!;
    } else if (qa.measurementOptionId != null &&
        measurement?.measurementOptions != null) {
      final option = measurement!.measurementOptions!.firstWhere(
        (opt) => opt.measurementOptionId == qa.measurementOptionId,
        orElse: () => entities.MeasurementOption(),
      );
      answerText = option.measurementOptionDescription ?? 'Selected Option';
    }

    return {'question': questionText, 'answer': answerText};
  }

  Widget _buildQuestionRow(String question, String response) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            question,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const Gap(16),
        Expanded(
          child: Text(
            response,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignatureSection(TextTheme textTheme, SignaturePageState state) {
    return _buildCardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Please sign below:',
              style: textTheme.montserratTitleExtraSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
            const Gap(16),
            _buildActionButtons(),
            const Gap(16),
            Container(
              height: 150,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.borderBlack),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Stack(
                  children: [
                    // Show signature canvas for drawing (bottom layer)
                    if (!_isNotAvailableSelected)
                      signature_package.Signature(
                        controller: _signatureController,
                        backgroundColor: Colors.transparent,
                      ),

                    // Show existing signature from URL or local file (middle layer)
                    if (state is SignaturePageLoaded &&
                        state.existingSignatureUrl != null &&
                        !_isNotAvailableSelected &&
                        !(state.isNotAvailable == true))
                      Positioned.fill(
                        child:
                            _buildSignatureImage(state.existingSignatureUrl!),
                      ),

                    // Show "not available" placeholder (top layer - highest priority)
                    if (_isNotAvailableSelected ||
                        (state is SignaturePageLoaded &&
                            state.isNotAvailable == true))
                      Positioned.fill(
                        child: Container(
                          color: Colors.grey[100],
                          child: const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.not_interested,
                                    size: 40, color: AppColors.blackTint1),
                                Gap(8),
                                Text('Not Available',
                                    style: TextStyle(
                                        color: AppColors.blackTint1,
                                        fontWeight: FontWeight.w500)),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds an Image widget that handles both network URLs and local file paths.
  Widget _buildSignatureImage(String imageUrl) {
    final isNetworkUrl =
        imageUrl.startsWith('http://') || imageUrl.startsWith('https://');

    final errorWidget = Center(
      child: Text(
        isNetworkUrl ? 'Could not load signature' : 'Signature file not found',
        style: const TextStyle(color: AppColors.loginRed, fontSize: 12),
      ),
    );

    if (isNetworkUrl) {
      return Image.network(
        imageUrl,
        fit: BoxFit.scaleDown,
        errorBuilder: (_, __, ___) => errorWidget,
        loadingBuilder: (_, child, progress) {
          if (progress == null) return child;
          return const Center(child: CircularProgressIndicator());
        },
      );
    } else {
      final file = File(imageUrl);
      return file.existsSync()
          ? Image.file(file,
              fit: BoxFit.scaleDown, errorBuilder: (_, __, ___) => errorWidget)
          : errorWidget;
    }
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: AppButton(
            text: 'Not available',
            color: _isNotAvailableSelected
                ? AppColors.primaryBlue
                : AppColors.blackTint1,
            textColor: Colors.white,
            onPressed: _handleNotAvailable,
            height: 40,
          ),
        ),
        const Gap(12),
        Expanded(
          child: AppButton(
            text: 'Clear',
            color: AppColors.loginRed,
            textColor: Colors.white,
            onPressed: _clearSignature,
            height: 40,
          ),
        ),
      ],
    );
  }

  Widget _buildPrintNameField(TextTheme textTheme) {
    return _buildCardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Print name',
              style: textTheme.montserratTitleExtraSmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
            const Gap(12),
            TextField(
              controller: _printNameController,
              style: textTheme.montserratParagraphSmall.copyWith(
                color: AppColors.black,
              ),
              decoration: InputDecoration(
                border: const OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.borderBlack),
                ),
                enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.borderBlack),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderSide:
                      BorderSide(color: AppColors.primaryBlue, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                hintText: 'Enter your name',
                hintStyle: textTheme.montserratParagraphSmall.copyWith(
                  color: AppColors.blackTint1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton(SignaturePageState state) {
    final isLoading = state is SignaturePageSaving;
    return Container(
      color: AppColors.lightGrey2,
      padding: const EdgeInsets.all(16),
      width: double.infinity,
      child: AppButton(
        text: isLoading ? 'Saving...' : 'Submit signature',
        onPressed: _canSubmit && !isLoading ? () => _handleSubmit() : () {},
        height: 50,
        color: _canSubmit ? AppColors.primaryBlue : AppColors.blackTint1,
      ),
    );
  }
}
