import 'dart:io';
import 'dart:typed_data';
import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/form_utils.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/signature_utils.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import 'signature_page_state.dart';

class SignaturePageCubit extends Cubit<SignaturePageState> {
  SignaturePageCubit() : super(SignaturePageInitial());

  /// Load form data and existing signature from database
  Future<void> loadFormData({
    required int taskId,
    required int formId,
  }) async {
    try {
      emit(SignaturePageLoading());

      // Load task and form data from database
      final taskWithForm = await FormUtils.getTaskWithFormModel(taskId, formId);

      if (taskWithForm == null) {
        emit(const SignaturePageError('Task or form not found in database'));
        return;
      }

      final taskEntity = taskWithForm['task'] as entities.TaskDetail;
      final formEntity =
          taskEntity.forms?.where((form) => form.formId == formId).firstOrNull;

      if (formEntity == null) {
        emit(const SignaturePageError('Form not found'));
        return;
      }

      // Load question answers for this form
      final questionAnswers = await _loadQuestionAnswers(taskId, formId);

      // Check for existing signature
      final existingSignatureData =
          await _getExistingSignatureData(taskId, formId);

      emit(SignaturePageLoaded(
        task: taskEntity,
        form: formEntity,
        questionAnswers: questionAnswers,
        existingSignatureUrl: existingSignatureData['url'],
        existingSignedBy: existingSignatureData['signedBy'],
        isNotAvailable: existingSignatureData['isNotAvailable'],
      ));
    } catch (e) {
      logger('SignaturePageCubit: Error loading form data: $e');
      emit(SignaturePageError('Error loading form data: ${e.toString()}'));
    }
  }

  /// Save signature to database and file storage
  Future<void> saveSignature({
    required int taskId,
    required int formId,
    required Uint8List signatureBytes,
    required String signedBy,
  }) async {
    final currentState = state;
    if (currentState is! SignaturePageLoaded) return;

    try {
      emit(SignaturePageSaving(
        task: currentState.task,
        form: currentState.form,
        questionAnswers: currentState.questionAnswers,
      ));

      // Save signature image to app storage
      final signatureUrl = await _saveSignatureImage(signatureBytes);
      if (signatureUrl == null) {
        emit(const SignaturePageError('Failed to save signature image'));
        return;
      }

      // Save signature to database
      await _saveSignatureToDatabase(
        taskId: taskId,
        formId: formId,
        signatureUrl: signatureUrl,
        signedBy: signedBy,
      );

      // Update the signature timestamp for the task
      await TaskUtils.updateTaskSignatureTimestamp(taskId.toString());

      emit(SignaturePageSaved(signatureUrl));
    } catch (e) {
      logger('SignaturePageCubit: Error saving signature: $e');
      emit(SignaturePageError('Error saving signature: ${e.toString()}'));
    }
  }

  /// Save "not available" signature record to database
  Future<void> saveNotAvailableSignature({
    required int taskId,
    required int formId,
    required String signedBy,
  }) async {
    final currentState = state;
    if (currentState is! SignaturePageLoaded) return;

    try {
      emit(SignaturePageSaving(
        task: currentState.task,
        form: currentState.form,
        questionAnswers: currentState.questionAnswers,
      ));

      // Save "not available" signature to database
      await _saveNotAvailableSignatureToDatabase(
        taskId: taskId,
        formId: formId,
        signedBy: signedBy,
      );

      // Update the signature timestamp for the task
      await TaskUtils.updateTaskSignatureTimestamp(taskId.toString());

      emit(const SignaturePageSaved(
          null)); // No signature URL for "not available"
    } catch (e) {
      logger('SignaturePageCubit: Error saving not available signature: $e');
      emit(SignaturePageError('Error saving signature: ${e.toString()}'));
    }
  }

  /// Clear existing signature from database and update state
  Future<void> clearExistingSignature({
    required int taskId,
    required int formId,
  }) async {
    final currentState = state;
    if (currentState is! SignaturePageLoaded) return;

    try {
      emit(SignaturePageSaving(
        task: currentState.task,
        form: currentState.form,
        questionAnswers: currentState.questionAnswers,
      ));

      // Delete existing signature from database using SignatureUtils
      final deleteSuccess = await SignatureUtils.deleteSignatureRecord(
        taskId: taskId,
        folderId: formId, // Use formId as folderId for signature folders
        deleteLocalFile: true,
      );

      if (deleteSuccess) {
        // Update state to remove existing signature data while preserving all other data
        emit(SignaturePageLoaded(
          task: currentState.task,
          form: currentState.form,
          questionAnswers:
              currentState.questionAnswers, // Preserve question answers
          existingSignatureUrl: null, // Clear existing signature
          existingSignedBy: null, // Clear existing signed by
          isNotAvailable: null, // Clear not available status
        ));

        logger(
            'Existing signature cleared successfully - preserved ${currentState.questionAnswers.length} question answers');
      } else {
        // Revert to previous state if deletion failed
        emit(currentState);
        emit(const SignaturePageError('Failed to clear existing signature'));
      }
    } catch (e) {
      logger('SignaturePageCubit: Error clearing existing signature: $e');
      emit(SignaturePageError('Error clearing signature: ${e.toString()}'));
    }
  }

  /// Load question answers for the form
  Future<List<entities.QuestionAnswer>> _loadQuestionAnswers(
      int taskId, int formId) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) return [];

      final formModel =
          taskModel.forms.where((form) => form.formId == formId).firstOrNull;

      if (formModel == null) return [];

      // Convert to entity and return question answers
      final taskEntity = TaskDetailMapper.toEntity(taskModel);
      final formEntity =
          taskEntity.forms?.where((form) => form.formId == formId).firstOrNull;

      return formEntity?.questionAnswers ?? [];
    } catch (e) {
      logger('Error loading question answers: $e');
      return [];
    }
  }

  /// Check for existing signature data including name and not-available status
  Future<Map<String, dynamic>> _getExistingSignatureData(
      int taskId, int formId) async {
    try {
      final realm = RealmDatabase.instance.realm;
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) {
        return {'url': null, 'signedBy': null, 'isNotAvailable': null};
      }

      // Find signature folder for this form
      final signatureFolder = taskModel.signatureFolder
          .where((folder) => folder.folderId == formId)
          .firstOrNull;

      if (signatureFolder == null || signatureFolder.signatures.isEmpty) {
        return {'url': null, 'signedBy': null, 'isNotAvailable': null};
      }

      // Get the most recent signature data
      final signature = signatureFolder.signatures.last;
      return {
        'url': signature.localPath ?? signature.signatureUrl,
        'signedBy': signature.signedBy,
        'isNotAvailable': signature.cannotUploadMandatory,
      };
    } catch (e) {
      logger('Error getting existing signature data: $e');
      return {'url': null, 'signedBy': null, 'isNotAvailable': null};
    }
  }

  /// Save signature image to app storage
  Future<String?> _saveSignatureImage(Uint8List signatureBytes) async {
    try {
      // Create a temporary file from the bytes
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tempFile =
          File(path.join(tempDir.path, 'signature_$timestamp.png'));
      await tempFile.writeAsBytes(signatureBytes);

      // Use ImageStorageUtils to save to app storage
      final savedPath = await ImageStorageUtils.saveImageToAppStorage(tempFile);

      // Clean up temporary file
      if (await tempFile.exists()) {
        await tempFile.delete();
      }

      return savedPath;
    } catch (e) {
      logger('Error saving signature image: $e');
      return null;
    }
  }

  /// Save signature to database
  Future<void> _saveSignatureToDatabase({
    required int taskId,
    required int formId,
    required String signatureUrl,
    required String signedBy,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      await realm.writeAsync(() {
        final taskModel =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        if (taskModel == null) return;

        // Find or create signature folder for this form
        var signatureFolder = taskModel.signatureFolder
            .where((folder) => folder.folderId == formId)
            .firstOrNull;

        if (signatureFolder == null) {
          signatureFolder = SignatureFolderModel(
            folderId: formId,
            folderName: 'Form $formId Signatures',
            modifiedTimeStampSignaturetype: DateTime.now(),
          );
          taskModel.signatureFolder.add(signatureFolder);
        }

        // Create new signature
        final signature = SignatureModel(
          signatureId: DateTime.now().millisecondsSinceEpoch,
          formId: formId,
          // Use localPath for locally saved signatures instead of signatureUrl
          localPath: signatureUrl,
          signedBy: signedBy,
          modifiedTimeStampSignature: DateTime.now(),
          userDeletedSignature: false,
          cannotUploadMandatory: false,
          isEdited: false,
        );

        signatureFolder.signatures.add(signature);
        signatureFolder.modifiedTimeStampSignaturetype = DateTime.now();
        taskModel.modifiedTimeStampSignatures = DateTime.now();
      });

      logger('Signature saved to database successfully');
    } catch (e) {
      logger('Error saving signature to database: $e');
      rethrow;
    }
  }

  /// Save "not available" signature record to database
  Future<void> _saveNotAvailableSignatureToDatabase({
    required int taskId,
    required int formId,
    required String signedBy,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      await realm.writeAsync(() {
        final taskModel =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        if (taskModel == null) return;

        // Find or create signature folder for this form
        var signatureFolder = taskModel.signatureFolder
            .where((folder) => folder.folderId == formId)
            .firstOrNull;

        if (signatureFolder == null) {
          signatureFolder = SignatureFolderModel(
            folderId: formId,
            folderName: 'Form $formId Signatures',
            modifiedTimeStampSignaturetype: DateTime.now(),
          );
          taskModel.signatureFolder.add(signatureFolder);
        }

        // Create "not available" signature record (matching Android logic)
        final signature = SignatureModel(
          signatureId: DateTime.now().millisecondsSinceEpoch,
          formId: formId,
          localPath: null, // No image file for "not available"
          signedBy: signedBy,
          modifiedTimeStampSignature: DateTime.now(),
          userDeletedSignature: false,
          cannotUploadMandatory:
              true, // Mark as "cannot upload mandatory" like Android
          isEdited: false,
        );

        signatureFolder.signatures.add(signature);
        signatureFolder.modifiedTimeStampSignaturetype = DateTime.now();
        taskModel.modifiedTimeStampSignatures = DateTime.now();
      });

      logger('Not available signature saved to database successfully');
    } catch (e) {
      logger('Error saving not available signature to database: $e');
      rethrow;
    }
  }

  /// Update an existing signature and mark it as edited
  /// This method can be used in the future if signature editing becomes a feature
  Future<void> updateExistingSignature({
    required int taskId,
    required int formId,
    required int signatureId,
    required String newLocalPath,
    String? newSignedBy,
  }) async {
    try {
      final realm = RealmDatabase.instance.realm;

      await realm.writeAsync(() {
        final taskModel =
            realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

        if (taskModel == null) return;

        // Find signature folder for this form
        final signatureFolder = taskModel.signatureFolder
            .where((folder) => folder.folderId == formId)
            .firstOrNull;

        if (signatureFolder == null) return;

        // Find the specific signature to update
        final signature = signatureFolder.signatures
            .where((sig) => sig.signatureId == signatureId)
            .firstOrNull;

        if (signature == null) return;

        // Update signature properties
        signature.localPath = newLocalPath;
        signature.isEdited = true;
        signature.modifiedTimeStampSignature = DateTime.now();

        if (newSignedBy != null) {
          signature.signedBy = newSignedBy;
        }

        // Update folder and task timestamps
        signatureFolder.modifiedTimeStampSignaturetype = DateTime.now();
        taskModel.modifiedTimeStampSignatures = DateTime.now();
      });

      logger('Signature updated and marked as edited successfully');
    } catch (e) {
      logger('Error updating signature: $e');
      rethrow;
    }
  }
}
